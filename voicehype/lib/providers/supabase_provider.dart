// lib/providers/supabase_provider.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supa;
import 'package:voicehype/env.dart';

part 'supabase_provider.g.dart';

Duration? supabaseProviderRetry(int retryCount, Object error) {
  return Duration(seconds: 3); // Retry every 5 seconds
}

@Riverpod(keepAlive: true, retry: supabaseProviderRetry)
class Supabase extends _$Supabase {
  @override
  Future<supa.SupabaseClient> build() async {
    // Initialize Supabase client
    final supabaseUrl = Env.supabaseUrl;
    final supabaseAnonKey = Env.supabaseAnonKey;

    if (supabaseUrl.isEmpty || supabaseAnonKey.isEmpty) {
      throw Exception(
        'Supabase URL or Anon Key is not set in environment variables.',
      );
    }

    // Initialize Supabase
    await supa.Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);

    return supa.Supabase.instance.client;
  }
}
