// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supabase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(Supabase)
const supabaseProvider = SupabaseProvider._();

final class SupabaseProvider
    extends $AsyncNotifierProvider<Supabase, supa.SupabaseClient> {
  const SupabaseProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'supabaseProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$supabaseHash();

  @$internal
  @override
  Supabase create() => Supabase();
}

String _$supabaseHash() => r'fbfd92cd40d974dd16aceaba2b5338bde434a192';

abstract class _$Supabase extends $AsyncNotifier<supa.SupabaseClient> {
  FutureOr<supa.SupabaseClient> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<supa.SupabaseClient>, supa.SupabaseClient>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<supa.SupabaseClient>, supa.SupabaseClient>,
              AsyncValue<supa.SupabaseClient>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
