import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:voicehype/objectbox.g.dart'; // created by `flutter pub run build_runner build`

part 'objectbox_provider.g.dart'; // This will be generated

// Keep the ObjectBox instance alive for the entire app lifecycle
// because the db needs to stay open obv
@Riverpod(keepAlive: true)
Future<ObjectBox> objectBox(ref) async {
  // Get application documents directory
  final docsDir = await getApplicationDocumentsDirectory();
  final storePath = p.join(docsDir.path, "voicehype-app-db");

  // Create and return the ObjectBox instance
  return ObjectBox.create(storePath: storePath);
}

class ObjectBox {
  late final Store store;

  ObjectBox._create(this.store) {
    // Add any additional setup code here
  }

  static Future<ObjectBox> create({required String storePath}) async {
    final store = await openStore(directory: storePath);
    return ObjectBox._create(store);
  }
}
