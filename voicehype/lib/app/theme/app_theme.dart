import 'package:flutter/material.dart';
import 'package:voicehype/app/theme/colors.dart';
import 'package:voicehype/app/theme/typography.dart';

/// VoiceHype App Theme
/// Provides consistent theming across the application
class VoiceHypeTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: const ColorScheme.light(
        primary: VoiceHypeColors.primary,
        onPrimary: Colors.black,
        secondary: VoiceHypeColors.secondary,
        onSecondary: Colors.white,
        surface: VoiceHypeColors.lightSurface,
        onSurface: VoiceHypeColors.lightTextPrimary,
        background: VoiceHypeColors.lightBackground,
        onBackground: VoiceHypeColors.lightTextPrimary,
        error: VoiceHypeColors.error,
        onError: Colors.white,
        outline: VoiceHypeColors.lightBorder,
      ),
      textTheme: _buildTextTheme(VoiceHypeColors.lightTextPrimary),
      appBarTheme: AppBarTheme(
        backgroundColor: VoiceHypeColors.lightBackground,
        foregroundColor: VoiceHypeColors.lightTextPrimary,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: VoiceHypeTypography.headlineMedium.copyWith(
          color: VoiceHypeColors.lightTextPrimary,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: VoiceHypeColors.primary,
          foregroundColor: Colors.black,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: VoiceHypeTypography.labelLarge,
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: VoiceHypeColors.primary,
          side: const BorderSide(color: VoiceHypeColors.primary),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: VoiceHypeTypography.labelLarge,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: VoiceHypeColors.lightBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: VoiceHypeColors.lightBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: VoiceHypeColors.primary, width: 2),
        ),
        filled: true,
        fillColor: VoiceHypeColors.lightBackground,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      cardTheme: CardTheme(
        color: VoiceHypeColors.lightSurface,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: const BorderSide(color: VoiceHypeColors.lightBorder),
        ),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: const ColorScheme.dark(
        primary: VoiceHypeColors.primary,
        onPrimary: Colors.black,
        secondary: VoiceHypeColors.secondary,
        onSecondary: Colors.white,
        surface: VoiceHypeColors.darkSurface,
        onSurface: VoiceHypeColors.darkTextPrimary,
        background: VoiceHypeColors.darkBackground,
        onBackground: VoiceHypeColors.darkTextPrimary,
        error: VoiceHypeColors.error,
        onError: Colors.white,
        outline: VoiceHypeColors.darkBorder,
      ),
      textTheme: _buildTextTheme(VoiceHypeColors.darkTextPrimary),
      appBarTheme: AppBarTheme(
        backgroundColor: VoiceHypeColors.darkBackground,
        foregroundColor: VoiceHypeColors.darkTextPrimary,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: VoiceHypeTypography.headlineMedium.copyWith(
          color: VoiceHypeColors.darkTextPrimary,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: VoiceHypeColors.primary,
          foregroundColor: Colors.black,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: VoiceHypeTypography.labelLarge,
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: VoiceHypeColors.primary,
          side: const BorderSide(color: VoiceHypeColors.primary),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: VoiceHypeTypography.labelLarge,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: VoiceHypeColors.darkBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: VoiceHypeColors.darkBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: VoiceHypeColors.primary, width: 2),
        ),
        filled: true,
        fillColor: VoiceHypeColors.darkSurface,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      cardTheme: CardTheme(
        color: VoiceHypeColors.darkSurface,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: const BorderSide(color: VoiceHypeColors.darkBorder),
        ),
      ),
    );
  }

  static TextTheme _buildTextTheme(Color textColor) {
    return TextTheme(
      displayLarge: VoiceHypeTypography.displayLarge.copyWith(color: textColor),
      displayMedium: VoiceHypeTypography.displayMedium.copyWith(color: textColor),
      displaySmall: VoiceHypeTypography.displaySmall.copyWith(color: textColor),
      headlineLarge: VoiceHypeTypography.headlineLarge.copyWith(color: textColor),
      headlineMedium: VoiceHypeTypography.headlineMedium.copyWith(color: textColor),
      headlineSmall: VoiceHypeTypography.headlineSmall.copyWith(color: textColor),
      titleLarge: VoiceHypeTypography.titleLarge.copyWith(color: textColor),
      titleMedium: VoiceHypeTypography.titleMedium.copyWith(color: textColor),
      titleSmall: VoiceHypeTypography.titleSmall.copyWith(color: textColor),
      bodyLarge: VoiceHypeTypography.bodyLarge.copyWith(color: textColor),
      bodyMedium: VoiceHypeTypography.bodyMedium.copyWith(color: textColor),
      bodySmall: VoiceHypeTypography.bodySmall.copyWith(color: textColor),
      labelLarge: VoiceHypeTypography.labelLarge.copyWith(color: textColor),
      labelMedium: VoiceHypeTypography.labelMedium.copyWith(color: textColor),
      labelSmall: VoiceHypeTypography.labelSmall.copyWith(color: textColor),
    );
  }
}
