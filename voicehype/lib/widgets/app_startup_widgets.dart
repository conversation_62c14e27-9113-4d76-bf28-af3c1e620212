import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voicehype/auth/auth_provider.dart';
import 'package:voicehype/auth/login_page.dart';
import 'package:voicehype/providers/app_startup_provider.dart';
import 'package:voicehype/providers/theme_provider.dart';
import 'package:voicehype/widgets/base_layout_page.dart';

// 1. Loading State Widget
class StartupLoadingWidget extends StatelessWidget {
  const StartupLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 20),
              Text(
                'Initializing app...',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 2. Success State Widget
class StartupSuccessWidget extends ConsumerWidget {
  const StartupSuccessWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeNotifierProvider).requireValue;

    return MaterialApp(
      title: 'VoiceHype',
      theme: ThemeData.light(),
      darkTheme: ThemeData.dark(),
      themeMode: themeMode,
      home: Consumer(
        builder: (context, ref, child) {
          bool isAuthenticated = ref.watch(isAuthenticatedProvider);

          return isAuthenticated ? BaseLayoutPage() : LoginPage();
        },
      ),
    );
  }
}

// 3. Error State Widget
class StartupErrorWidget extends ConsumerWidget {
  const StartupErrorWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 64),
                const SizedBox(height: 20),
                Text(
                  'Initialization Failed',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),
                Text(
                  'We couldn\'t initialize the app. Please check your connection and try again.',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  onPressed: () =>
                      ref.read(appStartupProvider.notifier).retry(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Main App that watches the startup provider
class MainApp extends ConsumerWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the app startup provider
    final appStartupState = ref.watch(appStartupProvider);

    return appStartupState.when(
      loading: () => const StartupLoadingWidget(),
      error: (error, stackTrace) => const StartupErrorWidget(),
      data: (isInitialized) => const StartupSuccessWidget(),
    );
  }
}
