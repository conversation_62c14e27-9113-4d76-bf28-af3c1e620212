// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'env.dart';

// **************************************************************************
// EnviedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// generated_from: .env
final class _Env {
  static const List<int> _enviedkeysupabaseUrl = <int>[
    3619772406,
    4280382013,
    1517198336,
    4187575673,
    4086124202,
    1343643519,
    1290328606,
    325233685,
    3894678772,
    1057628114,
    716303902,
    245376691,
    2863465147,
    487719255,
    2914928747,
    2117031781,
    2008560334,
    780545128,
    3381276315,
    73763964,
    24056514,
    2689322278,
    4069999146,
    3292779407,
    2136478404,
    3833150969,
    481952339,
    2813423152,
    2973219779,
  ];

  static const List<int> _envieddatasupabaseUrl = <int>[
    3619772318,
    4280382025,
    1517198452,
    4187575561,
    4086124249,
    1343643461,
    1290328625,
    325233722,
    3894678663,
    1057628071,
    716303982,
    245376722,
    2863465177,
    487719222,
    2914928664,
    2117031680,
    2008560352,
    780545054,
    3381276404,
    73763861,
    24056481,
    2689322307,
    4069999170,
    3292779510,
    2136478388,
    3833150876,
    481952381,
    2813423185,
    2973219754,
  ];

  static final String supabaseUrl = String.fromCharCodes(
    List<int>.generate(
      _envieddatasupabaseUrl.length,
      (int i) => i,
      growable: false,
    ).map((int i) => _envieddatasupabaseUrl[i] ^ _enviedkeysupabaseUrl[i]),
  );

  static const List<int> _enviedkeysupabaseAnonKey = <int>[
    637208657,
    1984526475,
    2948792102,
    965765061,
    1509959464,
    2454281501,
    1021150054,
    2767215455,
    2289889861,
    2460043107,
    1529605281,
    3877974126,
    1855013621,
    4020802218,
    3070378872,
    2117343025,
    3590208506,
    1900873207,
    387334005,
    661837865,
    3456292467,
    3673951429,
    4016538823,
    3217753168,
    1944673736,
    2050295008,
    687159228,
    1214422342,
    1390277272,
    1401284240,
    4238211839,
    2777504952,
    662569351,
    3089668754,
    2553956778,
    1612429951,
    1040925516,
    3610122658,
    2124507070,
    3532136284,
    1074312194,
    1594557092,
    2977353971,
    1850062028,
    2666609929,
    2778635778,
    474979259,
    3420593266,
    275988668,
    842498378,
    773280337,
    21355739,
    3454955998,
    3505504125,
    3388043764,
    4034995179,
    3491238695,
    1620245663,
    2320516027,
    4031835936,
    1335424777,
    3406536935,
    1486039729,
    1243269924,
    4218709814,
    260641644,
    257776749,
    2194686968,
    478505962,
    1067323432,
    3720174896,
    2858287005,
    1306718687,
    3568763246,
    1558246485,
    2540963418,
    554478555,
    996343365,
    2924555524,
    924956944,
    416938197,
    1887431520,
    1211976438,
    3977885959,
    2962889957,
    1165643141,
    1093518023,
    681608376,
    114902122,
    2801523468,
    3788545545,
    3924333733,
    3737354928,
    3465540253,
    1619672958,
    4229054555,
    4230240155,
    3508213168,
    1436526687,
    1972221248,
    4191909996,
    4288587285,
    4133741212,
    3344147135,
    2985363152,
    878649008,
    1665766789,
    972421930,
    3966267189,
    3624756370,
    4143914310,
    886635580,
    2694291429,
    2189715550,
    4247557406,
    3501376328,
    2929626545,
    2970464562,
    2772503970,
    34662119,
    3193729814,
    1949431430,
    345374442,
    2097426423,
    3019350212,
    3737331634,
    3289785606,
    3594072163,
    2004660092,
    2586020500,
    120471676,
    2481577791,
    1734410522,
    2144784889,
    4084255577,
    1395337831,
    629078517,
    91797648,
    976115713,
    3152341134,
    83923540,
    2735189685,
    2337806419,
    3960318161,
    4239965726,
    2620839432,
    2618284031,
    701407680,
    2185038983,
    565672544,
    2763594898,
    2152676521,
    1604142246,
    2945450728,
    404190049,
    1255944204,
    1866676418,
    2292574613,
    2396532638,
    4036565466,
    251201870,
    4220210014,
    3416011089,
    1646505386,
    3806349627,
    1960312607,
    669475280,
    605912327,
    1506192780,
  ];

  static const List<int> _envieddatasupabaseAnonKey = <int>[
    637208628,
    1984526578,
    2948792172,
    965765037,
    1509959498,
    2454281562,
    1021149957,
    2767215414,
    2289889802,
    2460043018,
    1529605355,
    3877974055,
    1855013536,
    4020802256,
    3070378801,
    2117342976,
    3590208436,
    1900873118,
    387333948,
    661837914,
    3456292410,
    3673951403,
    4016538773,
    3217753189,
    1944673707,
    2050294947,
    687159285,
    1214422384,
    1390277329,
    1401284347,
    4238211727,
    2777504992,
    662569425,
    3089668817,
    2553956832,
    1612429894,
    1040925538,
    3610122695,
    2124507079,
    3532136214,
    1074312315,
    1594557126,
    2977353921,
    1850062004,
    2666610021,
    2778635851,
    474979281,
    3420593181,
    275988693,
    842498323,
    773280262,
    21355758,
    3454955944,
    3505504031,
    3388043677,
    4034995106,
    3491238740,
    1620245718,
    2320516054,
    4031835980,
    1335424883,
    3406536836,
    1486039752,
    1243269997,
    4218709760,
    260641573,
    257776640,
    2194686910,
    478505859,
    1067323468,
    3720174951,
    2858287098,
    1306718631,
    3568763171,
    1558246463,
    2540963351,
    554478499,
    996343308,
    2924555629,
    924957031,
    416938172,
    1887431425,
    1211976353,
    3977886017,
    2962889941,
    1165643212,
    1093517997,
    681608407,
    114902034,
    2801523522,
    3788545651,
    3924333812,
    3737354882,
    3465540304,
    1619672836,
    4229054524,
    4230240170,
    3508213245,
    1436526645,
    1972221185,
    4191909915,
    4288587353,
    4133741279,
    3344147189,
    2985363132,
    878649045,
    1665766861,
    972421995,
    3966267228,
    3624756445,
    4143914284,
    886635641,
    2694291408,
    2189715475,
    4247557466,
    3501376281,
    2929626569,
    2970464636,
    2772504054,
    34662050,
    3193729828,
    1949431499,
    345374382,
    2097426357,
    3019350269,
    3737331612,
    3289785667,
    3594072072,
    2004659995,
    2586020575,
    120471583,
    2481577851,
    1734410619,
    2144784841,
    4084255503,
    1395337815,
    629078435,
    91797717,
    976115820,
    3152341214,
    83923495,
    2735189741,
    2337806339,
    3960318140,
    4239965799,
    2620839529,
    2618283923,
    701407731,
    2185039070,
    565672470,
    2763594986,
    2152676592,
    1604142291,
    2945450653,
    404190040,
    1255944285,
    1866676467,
    2292574718,
    2396532646,
    4036565397,
    251201812,
    4220209924,
    3416011047,
    1646505373,
    3806349692,
    1960312684,
    669475304,
    605912408,
    1506192867,
  ];

  static final String supabaseAnonKey = String.fromCharCodes(
    List<int>.generate(
      _envieddatasupabaseAnonKey.length,
      (int i) => i,
      growable: false,
    ).map(
      (int i) => _envieddatasupabaseAnonKey[i] ^ _enviedkeysupabaseAnonKey[i],
    ),
  );
}
