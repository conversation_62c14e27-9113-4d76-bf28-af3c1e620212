# B<PERSON>illah a<PERSON><PERSON><PERSON> ar-Raheem

## Starters

1. Create appStartupProvider ✅
2. Create themeProvider ✅
3. Create startup state widgets ✅
4. Use Yaru theme ✅
5. Create a splash screen (simple, white, logo)
6. Create a simple screen with navigation rail, that's it
7. All routes will be stateless, i.e. their state won't be preserved when navigating away
8. Create a supabase provider. Initialize it when user navigates to the Plan page, or does T | O | T + O

## Models

1. Controls (sample rate, language, optimization mode) -> shared_preferences
2. OptimizationMode -> ObjectBox
3. HistoryItem -> ObjectBox
4. PlanDetails -> Normal simple data class

## Recording

1. Create a recording service provider
2. Create one for linux especially in case record package doesn't work on a linux device

## API

1. Create transcription service provider
2. Create optimization service provider (just take in the messages array, don't hardcode here)

## Providers

1. Create a provider for transcription + optimization
2. Create a provider for settings (theme, language, etc.)
3. Create a provider for history
4. Create a provider for plan details

## Retry

1. Max three retries for transcription and optimization
2. If error code is INSUFFICIENT_CREDITS, don't try again, just return the result if <PERSON> failed but T succeeded
3. If T fails then don't try O because O depends on T

## Second window

1. Always on top
2. It's a small beautiful widget showing states (like recording, transcribing, optimizing, done)
3. It is interactive, i.e. clicking on it starts recording, etc.