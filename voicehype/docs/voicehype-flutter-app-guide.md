# VoiceHype Flutter App Development Guide

**<PERSON><PERSON><PERSON><PERSON> <PERSON>** - In the name of <PERSON>, the Most Gracious, the Most Merciful

## Overview

VoiceHype is a revolutionary voice-to-prompt service for developers working with LLMs, designed to eliminate the need for typing in most cases. This document contains essential information gathered from the existing VoiceHype ecosystem to guide the development of the Flutter desktop application.

## Core Mission

VoiceHype aims to render keyboards obsolete by using advanced speech recognition (Whisper/AssemblyAI) for transcription and LLMs (Claude, Llama, DeepSeek) for intelligent text optimization.

## Design System & Theme

### Color Palette

**Primary Colors:**
- Primary Green: `#14F195` (Bright mint green - signature VoiceHype color)
- Primary Hover: `#0ad680`
- Secondary: `#6366F1` (Indigo)
- Secondary Hover: `#4f46e5`
- Accent: `#0AD6DF` (Cyan)
- Accent Hover: `#08b8c0`

**Dark Mode Colors:**
- Background: `#0d1117` (GitHub-style dark)
- Surface: `#161b22`
- Surface Alt: `#21262d`
- Border: `#30363d`
- Text Primary: `#c9d1d9`
- Text Secondary: `#8b949e`

**Light Mode Colors:**
- Background: `#ffffff`
- Surface: `#f6f8fa`
- Border: `#d0d7de`
- Text Primary: `#24292f`
- Text Secondary: `#656d76`

### Typography

**Font Families:**
- Sans: `Inter` (Primary UI font)
- Heading: `Poppins` (For headings and emphasis)
- Mono: `Roboto Mono` (For code and technical content)

### Design Principles

1. **Minimalistic & Clean**: Focus on functionality over decoration
2. **Dark Mode First**: Primary experience is dark mode with light mode support
3. **Accessibility**: High contrast ratios and clear visual hierarchy
4. **Consistency**: Unified design language across all platforms

## Supported Languages (50+ Languages)

### AssemblyAI Best Model (20 languages):
- Global English, Australian English, British English, US English
- Spanish, French, German, Italian, Portuguese, Dutch
- Hindi, Japanese, Chinese, Finnish, Korean, Polish
- Russian, Turkish, Ukrainian, Vietnamese

### AssemblyAI Nano Model (100+ languages):
All AssemblyAI Best languages plus:
- Afrikaans, Albanian, Amharic, Arabic, Armenian, Assamese, Azerbaijani
- Bashkir, Basque, Belarusian, Bengali, Bosnian, Breton, Bulgarian
- Burmese, Catalan, Croatian, Czech, Danish, Estonian, Faroese
- Galician, Georgian, Greek, Gujarati, Haitian, Hausa, Hawaiian
- Hungarian, Icelandic, Indonesian, Javanese, Kannada, Kazakh
- Khmer, Lao, Latin, Latvian, Lingala, Lithuanian, Luxembourgish
- Macedonian, Malagasy, Malay, Malayalam, Maltese, Maori, Marathi
- Mongolian, Nepali, Norwegian, Norwegian Nynorsk, Occitan
- Panjabi, Pashto, Persian, Romanian, Sanskrit, Serbian, Shona
- Sindhi, Sinhala, Slovak, Slovenian, Somali, Sundanese, Swahili
- Swedish, Tagalog, Tajik, Tamil, Tatar, Telugu, Thai, Tibetan
- Turkmen, Urdu, Uzbek, Welsh, Yiddish, Yoruba

### Whisper/LemonFox Models (50+ languages):
- All major world languages including Arabic, Chinese, Hindi, etc.

## Optimization Models

### Claude Models:
- **Claude 4 Sonnet** (Default - most advanced)
- **Claude 3.7 Sonnet** (Latest with excellent reasoning)
- **Claude 3.5 Sonnet** (Balanced performance)
- **Claude Haiku** (Fast and efficient)

### Llama Models:
- **Llama 3.1 70B** (Strong performance)
- **Llama 3.1 8B Instruct Turbo** (Speed optimized)

### DeepSeek Models:
- **DeepSeek V3** (Latest model)

## Optimization Modes (Prompt Templates)

### Built-in Modes:
1. **Clean Up** - Remove filler words, fix grammar, maintain meaning
2. **Translate** - Translate to target language with cultural context
3. **Summarize** - Create coherent paragraph capturing key ideas
4. **Polish** - Make formal and professional
5. **Expand** - Elaborate into detailed explanation
6. **Light Touch** - Minimal editing, keep casual tone
7. **Bullet Points** - Convert to concise bullet list
8. **Islamic Tone** - Add Islamic guidance and motivation
9. **WhatsApp Style** - Short, casual messaging format
10. **Email Format** - Professional email structure
11. **Reminder Note** - Brief personal reminder
12. **To-Do List** - Extract actionable tasks
13. **Meeting Minutes** - Structured meeting format
14. **Social Caption** - Engaging social media format

### Custom Modes:
- Users can create custom optimization modes
- Save with custom names and prompts
- Delete custom modes (built-in modes cannot be deleted)

## Keyboard Shortcuts

Essential shortcuts for the Flutter app:
- **Ctrl+Alt+8**: Start recording (transcription only)
- **Ctrl+Alt+9**: Start recording with optimization
- **Ctrl+Alt+0**: Pause/Resume recording
- **Ctrl+Alt+-**: Cancel recording
- **Ctrl+Alt+.**: Edit selected text (if text is selected in OS)

## Services & Models

### Transcription Services:
1. **AssemblyAI** (Primary)
   - Best model (limited languages, high accuracy)
   - Nano model (100+ languages)
   - Real-time transcription support
2. **Whisper** (OpenAI)
3. **LemonFox** (Alternative to Whisper)

### Audio Settings:
- Sample rates: 8000, 16000, 22050, 44100, 48000 Hz
- Device selection for input
- Real-time transcription (AssemblyAI Best only)

## Technical Architecture

### State Management:
- **Riverpod** for state management
- **ObjectBox** for local database
- **Supabase** for backend services

### Key Providers:
- `ThemeNotifier` - Theme management
- `AuthProvider` - Authentication state
- `AppStartupProvider` - App initialization
- `SupabaseProvider` - Backend connection

### Data Models:
- `OptimizationMode` - Custom prompt templates
- `Controls` - User settings and preferences
- `HistoryItem` - Transcription history

## Authentication & Backend

### Supabase Integration:
- OAuth providers: Google, GitHub
- Self-hosted Supabase instance
- Row Level Security (RLS) policies
- API key management for secure access

### Environment:
- Production: `supabase.voicehype.ai`
- Development: Local Supabase instance

## Pricing & Business Model

### Subscription Tiers:
- **Basic**: $9/month
- **Professional**: $18/month
- **Enterprise**: $27/month

### Hybrid Pricing:
- Monthly subscriptions with credit overage
- Pay-as-you-go option
- Credit-based system for flexibility

## Islamic Values Integration

VoiceHype operates with Islamic values:
- Partnership between Kifayat Ullah and Bilal Tariq
- Support for Palestine (donation links)
- Islamic tone optimization mode
- Halal business practices

## Privacy & Security

### Core Principles:
- **No data storage**: VoiceHype never stores user transcripts or optimized prompts
- **Privacy first**: All processing is ephemeral
- **Secure authentication**: OAuth and API key based access
- **Local storage**: History stored locally on user device

## UI/UX Guidelines

### Home Page Layout:
**Left Side (Controls):**
- VoiceHype logo at top
- Language selector dropdown
- Optimization model selector
- Optimization mode pills (cylindrical buttons)
- Plus button for creating custom modes

**Right Side (Shortcuts):**
- Beautiful, minimalistic shortcut display
- Visual representation of key combinations
- Clear action descriptions

### Design Patterns:
- **Cylindrical pills** for mode selection
- **Dropdown selectors** for languages and models
- **Minimalistic cards** for shortcuts
- **Consistent spacing** and typography
- **Smooth animations** and transitions

## Development Priorities

1. **UI First**: Perfect the home page interface
2. **Recording Service**: Implement audio recording
3. **Controls Repository**: Settings management
4. **Integration**: Connect with VoiceHype API
5. **Testing**: Comprehensive testing across platforms

## Platform Support

- **Primary**: Desktop (Windows, macOS, Linux)
- **Future**: Mobile (iOS, Android)
- **Integration**: VS Code extension compatibility

## API Integration

### VoiceHype API Endpoints:
- **Base URL**: `http://***************:3001` (DigitalOcean server)
- **Transcribe**: `/transcribe` - Audio to text conversion
- **Optimize**: `/optimize` - Text optimization using LLMs
- **Transcribe + Optimize**: `/transcribe-optimize` - Combined workflow
- **Real-time**: `/realtime` - WebSocket for live transcription

### Request/Response Format:
```json
{
  "data": {
    "optimizedText": "...",
    "metadata": {
      "model": "claude-4-sonnet",
      "usage": {
        "inputTokens": 150,
        "outputTokens": 200
      }
    }
  }
}
```

## File Structure Recommendations

```
lib/
├── main.dart
├── app/
│   ├── app.dart
│   └── theme/
│       ├── app_theme.dart
│       ├── colors.dart
│       └── typography.dart
├── features/
│   ├── home/
│   │   ├── presentation/
│   │   │   ├── pages/
│   │   │   ├── widgets/
│   │   │   └── providers/
│   │   ├── domain/
│   │   └── data/
│   ├── recording/
│   ├── history/
│   └── settings/
├── shared/
│   ├── widgets/
│   ├── utils/
│   └── constants/
└── core/
    ├── services/
    ├── models/
    └── providers/
```

## Key Dependencies

### Current Dependencies:
```yaml
dependencies:
  flutter_riverpod: ^3.0.0-dev.17
  objectbox: ^4.3.1
  record: ^6.1.1
  audioplayers: ^6.5.1
  window_manager: ^0.5.1
  hotkey_manager: ^0.2.3
  supabase_flutter: ^2.10.0
  shared_preferences: ^2.1.5
```

### Recommended Additions:
```yaml
dependencies:
  http: ^1.1.0  # For API calls
  json_annotation: ^4.8.1  # For JSON serialization
  flutter_svg: ^2.0.9  # For SVG logo support
  url_launcher: ^6.2.1  # For external links
  package_info_plus: ^4.2.0  # For app version info
```

## Implementation Roadmap

### Phase 1: UI Foundation
1. Setup app theme with VoiceHype colors
2. Create home page layout structure
3. Implement logo component
4. Build language selector dropdown
5. Create optimization model selector
6. Design optimization mode pills

### Phase 2: Core Functionality
1. Implement recording service
2. Add keyboard shortcut handling
3. Create settings management
4. Build history storage
5. Add audio device selection

### Phase 3: API Integration
1. Setup HTTP client for VoiceHype API
2. Implement transcription service
3. Add optimization service
4. Create real-time transcription
5. Handle error states and retries

### Phase 4: Polish & Testing
1. Add animations and transitions
2. Implement comprehensive error handling
3. Create unit and integration tests
4. Performance optimization
5. Platform-specific adjustments

## Error Handling Strategy

### Network Errors:
- Retry logic with exponential backoff
- Offline mode with local storage
- User-friendly error messages
- Connection status indicators

### Audio Errors:
- Microphone permission handling
- Device availability checks
- Audio format validation
- Recording failure recovery

### API Errors:
- Rate limiting handling
- Authentication token refresh
- Service unavailability fallbacks
- Graceful degradation

## Performance Considerations

### Audio Processing:
- Efficient audio compression
- Chunked upload for large files
- Background processing
- Memory management for recordings

### UI Performance:
- Lazy loading for history
- Efficient state management
- Minimal rebuilds with Riverpod
- Smooth animations at 60fps

### Storage Optimization:
- Local database indexing
- Automatic cleanup of old records
- Compressed audio storage
- Efficient query patterns

---

*May Allah bless this project and make it beneficial for the Ummah. Ameen.*
